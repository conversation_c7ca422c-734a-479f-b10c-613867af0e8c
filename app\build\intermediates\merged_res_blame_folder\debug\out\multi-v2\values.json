{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values/values.xml", "map": [{"source": "G:\\testmobileapp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "42", "endOffsets": "54"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6335", "endColumns": "42", "endOffsets": "6373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,104,111,112,113,114,115,116,117,173,202,203,207,208,212,216,217,218,224,234,267,288,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,948,1018,1086,1158,1228,1289,1363,1436,1497,1558,1620,1684,1746,1807,1875,1975,2035,2101,2174,2243,2300,2352,2414,2486,2562,4884,4919,5063,5118,5181,5236,5294,5352,5413,5476,5533,5584,5634,5695,5752,5818,5852,5887,6182,6701,6768,6840,6909,6978,7052,7124,10853,12462,12579,12780,12890,13091,13311,13383,13450,13653,13954,15685,16366,17048", "endLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,104,111,112,113,114,115,116,117,173,202,206,207,211,212,216,217,223,233,266,287,320,326", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,607,670,1013,1081,1153,1223,1284,1358,1431,1492,1553,1615,1679,1741,1802,1870,1970,2030,2096,2169,2238,2295,2347,2409,2481,2557,2622,4914,4949,5113,5176,5231,5289,5347,5408,5471,5528,5579,5629,5690,5747,5813,5847,5882,5917,6247,6763,6835,6904,6973,7047,7119,7207,10919,12574,12775,12885,13086,13215,13378,13445,13648,13949,15680,16361,17043,17210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\637f8c04801887031da0ccafb3cf3816\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6079", "endColumns": "49", "endOffsets": "6124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a3617a96bb7c392189dda1fe0f3a5ab8\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6252", "endColumns": "82", "endOffsets": "6330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8c2e0c3da23ddd9b68114be347ff71a\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "6025", "endColumns": "53", "endOffsets": "6074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "107,108,109,110,120,121,122,123,124,125,128,129,130,131,132,133,134,135,136,137,138,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,158,160,163,170,172,174,178,179,180,181,182,183,184,185,186,187,188,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6378,6462,6544,6621,7331,7379,7440,7519,7621,7703,7819,7869,7934,7991,8056,8141,8232,8302,8395,8484,8578,8723,8810,8894,8986,9080,9140,9204,9287,9377,9440,9508,9576,9673,9778,9850,10082,10184,10342,10701,10800,10924,11165,11211,11261,11328,11395,11461,11526,11580,11652,11719,11789,11871,11917,11983", "endLines": "107,108,109,110,120,121,122,123,124,127,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,158,160,163,170,172,174,178,179,180,181,182,183,184,185,186,187,188,189,190,191", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "6457,6539,6616,6696,7374,7435,7514,7616,7698,7814,7864,7929,7986,8051,8136,8227,8297,8390,8479,8573,8718,8805,8889,8981,9075,9135,9199,9282,9372,9435,9503,9571,9668,9773,9845,9910,10121,10225,10406,10749,10848,10987,11206,11256,11323,11390,11456,11521,11575,11647,11714,11784,11866,11912,11978,12039"}}, {"source": "G:\\testmobileapp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,675,722,769,816,861,906", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,717,764,811,856,901,943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,103,118,119,156,157,159,161,162,164,165,166,167,168,169,171,175,176,177,192,195,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2627,2686,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3345,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4004,4063,4123,4183,4242,4301,4360,4419,4478,4537,4611,4669,4724,4775,6129,7212,7277,9915,9981,10126,10230,10282,10411,10473,10527,10563,10597,10647,10754,10992,11039,11075,12044,12156,12267", "endLines": "41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,103,118,119,156,157,159,161,162,164,165,166,167,168,169,171,175,176,177,194,197,201", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2681,2740,2800,2860,2920,2980,3040,3100,3160,3220,3280,3340,3399,3459,3519,3579,3639,3699,3759,3819,3879,3939,3999,4058,4118,4178,4237,4296,4355,4414,4473,4532,4606,4664,4719,4770,4825,6177,7272,7326,9976,10077,10179,10277,10337,10468,10522,10558,10592,10642,10696,10795,11034,11070,11160,12151,12262,12457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b64b95852f06f4f48e4e0f5f368290ea\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "82,100", "startColumns": "4,4", "startOffsets": "5021,5965", "endColumns": "41,59", "endOffsets": "5058,6020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\be09877cc53f105ec1fa77a6f1a1cb3e\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "5922", "endColumns": "42", "endOffsets": "5960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\95514292aa8f77c22571cd8d9ad129a1\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "78,81", "startColumns": "4,4", "startOffsets": "4830,4954", "endColumns": "53,66", "endOffsets": "4879,5016"}}, {"source": "G:\\testmobileapp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "5", "endColumns": "12", "endOffsets": "260"}, "to": {"startLines": "213", "startColumns": "4", "startOffsets": "13220", "endLines": "215", "endColumns": "12", "endOffsets": "13306"}}]}]}