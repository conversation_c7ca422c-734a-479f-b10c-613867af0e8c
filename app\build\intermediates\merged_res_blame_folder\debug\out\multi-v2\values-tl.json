{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,999,1068,1155,1241,1312,1390,1456", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,994,1063,1150,1236,1307,1385,1451,1578"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1308,1407,5251,5348,5522,5691,5773,5865,5957,6041,6111,6180,6267,6438,6798,6876,6942", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "1402,1488,5343,5445,5607,5768,5860,5952,6036,6106,6175,6262,6348,6504,6871,6937,7064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "574,671,773,874,971,1078,1186,6594", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "666,768,869,966,1073,1181,1303,6690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,407,524,606,702,816,959,1082,1228,1309,1404,1495,1589,1706,1840,1940,2083,2227,2370,2536,2670,2789,2911,3033,3125,3220,3341,3472,3575,3675,3784,3924,4069,4181,4282,4354,4433,4518,4603,4706,4782,4862,4958,5058,5154,5256,5340,5448,5549,5654,5769,5845,5948", "endColumns": "119,117,113,116,81,95,113,142,122,145,80,94,90,93,116,133,99,142,143,142,165,133,118,121,121,91,94,120,130,102,99,108,139,144,111,100,71,78,84,84,102,75,79,95,99,95,101,83,107,100,104,114,75,102,90", "endOffsets": "170,288,402,519,601,697,811,954,1077,1223,1304,1399,1490,1584,1701,1835,1935,2078,2222,2365,2531,2665,2784,2906,3028,3120,3215,3336,3467,3570,3670,3779,3919,4064,4176,4277,4349,4428,4513,4598,4701,4777,4857,4953,5053,5149,5251,5335,5443,5544,5649,5764,5840,5943,6034"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,343,457,1493,1575,1671,1785,1928,2051,2197,2278,2373,2464,2558,2675,2809,2909,3052,3196,3339,3505,3639,3758,3880,4002,4094,4189,4310,4441,4544,4644,4753,4893,5038,5150,5450,5612,6353,6509,6695,7069,7145,7225,7321,7421,7517,7619,7703,7811,7912,8017,8132,8208,8311", "endColumns": "119,117,113,116,81,95,113,142,122,145,80,94,90,93,116,133,99,142,143,142,165,133,118,121,121,91,94,120,130,102,99,108,139,144,111,100,71,78,84,84,102,75,79,95,99,95,101,83,107,100,104,114,75,102,90", "endOffsets": "220,338,452,569,1570,1666,1780,1923,2046,2192,2273,2368,2459,2553,2670,2804,2904,3047,3191,3334,3500,3634,3753,3875,3997,4089,4184,4305,4436,4539,4639,4748,4888,5033,5145,5246,5517,5686,6433,6589,6793,7140,7220,7316,7416,7512,7614,7698,7806,7907,8012,8127,8203,8306,8397"}}]}]}