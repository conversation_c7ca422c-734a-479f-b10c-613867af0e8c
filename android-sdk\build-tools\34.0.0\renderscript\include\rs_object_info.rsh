/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Don't edit this file!  It is auto-generated by frameworks/rs/api/generate.sh.

/*
 * rs_object_info.rsh: Object Characteristics Functions
 *
 * The functions below can be used to query the characteristics of an Allocation, Element,
 * or Sampler object.  These objects are created from Java.  You can't create them from a
 * script.
 *
 * Allocations:
 *
 * Allocations are the primary method used to pass data to and from RenderScript kernels.
 *
 * They are a structured collection of cells that can be used to store bitmaps, textures,
 * arbitrary data points, etc.
 *
 * This collection of cells may have many dimensions (X, Y, Z, Array0, Array1, <PERSON>rray2, <PERSON>rray3),
 * faces (for cubemaps), and level of details (for mipmapping).
 *
 * See the android.renderscript.Allocation for details on to create Allocations.
 *
 * Elements:
 *
 * The term "element" is used a bit ambiguously in RenderScript, as both type information
 * for the cells of an Allocation and the instantiation of that type.  For example:
 * - rs_element is a handle to a type specification, and
 * - In functions like rsGetElementAt(), "element" means the instantiation of the type,
 *     i.e. a cell of an Allocation.
 *
 * The functions below let you query the characteristics of the type specificiation.
 *
 * An Element can specify a simple data types as found in C, e.g. an integer, float, or
 * boolean.  It can also specify a handle to a RenderScript object.  See rs_data_type for
 * a list of basic types.
 *
 * Elements can specify fixed size vector (of size 2, 3, or 4) versions of the basic types.
 * Elements can be grouped together into complex Elements, creating the equivalent of
 * C structure definitions.
 *
 * Elements can also have a kind, which is semantic information used to interpret pixel
 * data.  See rs_data_kind.
 *
 * When creating Allocations of common elements, you can simply use one of the many predefined
 * Elements like F32_2.
 *
 * To create complex Elements, use the Element.Builder Java class.
 *
 * Samplers:
 *
 * Samplers objects define how Allocations can be read as structure within a kernel.
 * See android.renderscript.S.
 */

#ifndef RENDERSCRIPT_RS_OBJECT_INFO_RSH
#define RENDERSCRIPT_RS_OBJECT_INFO_RSH

/*
 * rsAllocationGetDimFaces: Presence of more than one face
 *
 * If the Allocation is a cubemap, this function returns 1 if there's more than one face
 * present.  In all other cases, it returns 0.
 *
 * Use rsGetDimHasFaces() to get the dimension of a currently running kernel.
 *
 * Returns: Returns 1 if more than one face is present, 0 otherwise.
 */
extern uint32_t __attribute__((overloadable))
    rsAllocationGetDimFaces(rs_allocation a);

/*
 * rsAllocationGetDimLOD: Presence of levels of detail
 *
 * Query an Allocation for the presence of more than one Level Of Detail.  This is useful
 * for mipmaps.
 *
 * Use rsGetDimLod() to get the dimension of a currently running kernel.
 *
 * Returns: Returns 1 if more than one LOD is present, 0 otherwise.
 */
extern uint32_t __attribute__((overloadable))
    rsAllocationGetDimLOD(rs_allocation a);

/*
 * rsAllocationGetDimX: Size of the X dimension
 *
 * Returns the size of the X dimension of the Allocation.
 *
 * Use rsGetDimX() to get the dimension of a currently running kernel.
 *
 * Returns: X dimension of the Allocation.
 */
extern uint32_t __attribute__((overloadable))
    rsAllocationGetDimX(rs_allocation a);

/*
 * rsAllocationGetDimY: Size of the Y dimension
 *
 * Returns the size of the Y dimension of the Allocation.  If the Allocation has less
 * than two dimensions, returns 0.
 *
 * Use rsGetDimY() to get the dimension of a currently running kernel.
 *
 * Returns: Y dimension of the Allocation.
 */
extern uint32_t __attribute__((overloadable))
    rsAllocationGetDimY(rs_allocation a);

/*
 * rsAllocationGetDimZ: Size of the Z dimension
 *
 * Returns the size of the Z dimension of the Allocation.  If the Allocation has less
 * than three dimensions, returns 0.
 *
 * Use rsGetDimZ() to get the dimension of a currently running kernel.
 *
 * Returns: Z dimension of the Allocation.
 */
extern uint32_t __attribute__((overloadable))
    rsAllocationGetDimZ(rs_allocation a);

/*
 * rsAllocationGetElement: Get the object that describes the cell of an Allocation
 *
 * Get the Element object describing the type, kind, and other characteristics of a cell
 * of an Allocation.  See the rsElement* functions below.
 *
 * Parameters:
 *   a: Allocation to get data from.
 *
 * Returns: Element describing Allocation layout.
 */
extern rs_element __attribute__((overloadable))
    rsAllocationGetElement(rs_allocation a);

/*
 * rsClearObject: Release an object
 *
 * Tells the run time that this handle will no longer be used to access the the related
 * object.  If this was the last handle to that object, resource recovery may happen.
 *
 * After calling this function, *dst will be set to an empty handle.  See rsIsObject().
 */
extern void __attribute__((overloadable))
    rsClearObject(rs_element* dst);

extern void __attribute__((overloadable))
    rsClearObject(rs_type* dst);

extern void __attribute__((overloadable))
    rsClearObject(rs_allocation* dst);

extern void __attribute__((overloadable))
    rsClearObject(rs_sampler* dst);

extern void __attribute__((overloadable))
    rsClearObject(rs_script* dst);

/*
 * rsIsObject: Check for an empty handle
 *
 * Returns true if the handle contains a non-null reference.
 *
 * This function does not validate that the internal pointer used in the handle
 * points to an actual valid object; it only checks for null.
 *
 * This function can be used to check the Element returned by rsElementGetSubElement()
 * or see if rsClearObject() has been called on a handle.
 */
extern bool __attribute__((overloadable))
    rsIsObject(rs_element v);

extern bool __attribute__((overloadable))
    rsIsObject(rs_type v);

extern bool __attribute__((overloadable))
    rsIsObject(rs_allocation v);

extern bool __attribute__((overloadable))
    rsIsObject(rs_sampler v);

extern bool __attribute__((overloadable))
    rsIsObject(rs_script v);

/*
 * rsElementGetBytesSize: Size of an Element
 *
 * Returns the size in bytes that an instantiation of this Element will occupy.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetBytesSize(rs_element e);
#endif

/*
 * rsElementGetDataKind: Kind of an Element
 *
 * Returns the Element's data kind.  This is used to interpret pixel data.
 *
 * See rs_data_kind.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_data_kind __attribute__((overloadable))
    rsElementGetDataKind(rs_element e);
#endif

/*
 * rsElementGetDataType: Data type of an Element
 *
 * Returns the Element's base data type.  This can be a type similar to C/C++ (e.g.
 * RS_TYPE_UNSIGNED_8), a handle (e.g. RS_TYPE_ALLOCATION and RS_TYPE_ELEMENT), or a
 * more complex numerical type (e.g. RS_TYPE_UNSIGNED_5_6_5 and RS_TYPE_MATRIX_4X4).
 * See rs_data_type.
 *
 * If the Element describes a vector, this function returns the data type of one of its items.
 * Use rsElementGetVectorSize to get the size of the vector.
 *
 * If the Element describes a structure, RS_TYPE_NONE is returned.  Use the rsElementGetSub*
 * functions to explore this complex Element.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_data_type __attribute__((overloadable))
    rsElementGetDataType(rs_element e);
#endif

/*
 * rsElementGetSubElement: Sub-element of a complex Element
 *
 * For Elements that represents a structure, this function returns the sub-element at the
 * specified index.
 *
 * If the Element is not a structure or the index is greater or equal to the number of
 * sub-elements, an invalid handle is returned.
 *
 * Parameters:
 *   e: Element to query.
 *   index: Index of the sub-element to return.
 *
 * Returns: Sub-element at the given index.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_element __attribute__((overloadable))
    rsElementGetSubElement(rs_element e, uint32_t index);
#endif

/*
 * rsElementGetSubElementArraySize: Array size of a sub-element of a complex Element
 *
 * For complex Elements, sub-elements can be statically sized arrays.  This function
 * returns the array size of the sub-element at the index.  This sub-element repetition
 * is different than fixed size vectors.
 *
 * Parameters:
 *   e: Element to query.
 *   index: Index of the sub-element.
 *
 * Returns: Array size of the sub-element.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetSubElementArraySize(rs_element e, uint32_t index);
#endif

/*
 * rsElementGetSubElementCount: Number of sub-elements
 *
 * Elements can be simple, such as an int or a float, or a structure with multiple
 * sub-elements.  This function returns zero for simple Elements and the number of
 * sub-elements for complex Elements.
 *
 * Parameters:
 *   e: Element to get data from.
 *
 * Returns: Number of sub-elements.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetSubElementCount(rs_element e);
#endif

/*
 * rsElementGetSubElementName: Name of a sub-element
 *
 * For complex Elements, this function returns the name of the sub-element at the
 * specified index.
 *
 * Parameters:
 *   e: Element to get data from.
 *   index: Index of the sub-element.
 *   name: Address of the array to store the name into.
 *   nameLength: Length of the provided name array.
 *
 * Returns: Number of characters copied, excluding the null terminator.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetSubElementName(rs_element e, uint32_t index, char* name, uint32_t nameLength);
#endif

/*
 * rsElementGetSubElementNameLength: Length of the name of a sub-element
 *
 * For complex Elements, this function returns the length of the name of the sub-element
 * at the specified index.
 *
 * Parameters:
 *   e: Element to get data from.
 *   index: Index of the sub-element.
 *
 * Returns: Length of the sub-element name including the null terminator.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetSubElementNameLength(rs_element e, uint32_t index);
#endif

/*
 * rsElementGetSubElementOffsetBytes: Offset of the instantiated sub-element
 *
 * This function returns the relative position of the instantiation of the specified
 * sub-element within the instantiation of the Element.
 *
 * For example, if the Element describes a 32 bit float followed by a 32 bit integer,
 * the offset return for the first will be 0 and the second 4.
 *
 * Parameters:
 *   e: Element to get data from.
 *   index: Index of the sub-element.
 *
 * Returns: Offset in bytes.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetSubElementOffsetBytes(rs_element e, uint32_t index);
#endif

/*
 * rsElementGetVectorSize: Vector size of the Element
 *
 * Returns the Element's vector size.  If the Element does not represent a vector,
 * 1 is returned.
 *
 * Parameters:
 *   e: Element to get data from.
 *
 * Returns: Length of the element vector.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern uint32_t __attribute__((overloadable))
    rsElementGetVectorSize(rs_element e);
#endif

/*
 * rsGetAllocation: Return the Allocation for a given pointer
 *
 * DEPRECATED.  Do not use.
 *
 * Returns the Allocation for a given pointer.  The pointer should point within a valid
 * allocation.  The results are undefined if the pointer is not from a valid Allocation.
 */
extern rs_allocation __attribute__((overloadable
#if (defined(RS_VERSION) && (RS_VERSION >= 22))
, deprecated("This function is deprecated and will be removed from the SDK in a future release.")
#endif
))
    rsGetAllocation(const void* p);

/*
 * rsSamplerGetAnisotropy: Anisotropy of the Sampler
 *
 * Get the Sampler's anisotropy.
 *
 * See android.renderscript.S.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern float __attribute__((overloadable))
    rsSamplerGetAnisotropy(rs_sampler s);
#endif

/*
 * rsSamplerGetMagnification: Sampler magnification value
 *
 * Get the Sampler's magnification value.
 *
 * See android.renderscript.S.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_sampler_value __attribute__((overloadable))
    rsSamplerGetMagnification(rs_sampler s);
#endif

/*
 * rsSamplerGetMinification: Sampler minification value
 *
 * Get the Sampler's minification value.
 *
 * See android.renderscript.S.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_sampler_value __attribute__((overloadable))
    rsSamplerGetMinification(rs_sampler s);
#endif

/*
 * rsSamplerGetWrapS: Sampler wrap S value
 *
 * Get the Sampler's wrap S value.
 *
 * See android.renderscript.S.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_sampler_value __attribute__((overloadable))
    rsSamplerGetWrapS(rs_sampler s);
#endif

/*
 * rsSamplerGetWrapT: Sampler wrap T value
 *
 * Get the sampler's wrap T value.
 *
 * See android.renderscript.S.
 */
#if (defined(RS_VERSION) && (RS_VERSION >= 16))
extern rs_sampler_value __attribute__((overloadable))
    rsSamplerGetWrapT(rs_sampler s);
#endif

/*
 * rsSetObject: For internal use.
 *
 */
extern void __attribute__((overloadable))
    rsSetObject(rs_element* dst, rs_element src);

extern void __attribute__((overloadable))
    rsSetObject(rs_type* dst, rs_type src);

extern void __attribute__((overloadable))
    rsSetObject(rs_allocation* dst, rs_allocation src);

extern void __attribute__((overloadable))
    rsSetObject(rs_sampler* dst, rs_sampler src);

extern void __attribute__((overloadable))
    rsSetObject(rs_script* dst, rs_script src);

#endif // RENDERSCRIPT_RS_OBJECT_INFO_RSH
