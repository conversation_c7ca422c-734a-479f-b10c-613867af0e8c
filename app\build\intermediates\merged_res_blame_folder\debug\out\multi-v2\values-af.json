{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1383,5063,5160,5334,5496,5572,5663,5753,5839,5903,5968,6046,6208,6565,6646,6716", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "1378,1465,5155,5256,5415,5567,5658,5748,5834,5898,5963,6041,6122,6274,6641,6711,6831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "555,653,755,853,951,1058,1167,6363", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "648,750,848,946,1053,1162,1282,6459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,282,393,505,581,681,799,936,1060,1210,1291,1386,1472,1571,1682,1799,1899,2023,2144,2272,2434,2555,2673,2793,2919,3006,3101,3213,3335,3431,3536,3635,3767,3903,4005,4098,4171,4247,4328,4412,4513,4590,4669,4764,4858,4949,5043,5127,5226,5322,5420,5532,5609,5705", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "163,277,388,500,576,676,794,931,1055,1205,1286,1381,1467,1566,1677,1794,1894,2018,2139,2267,2429,2550,2668,2788,2914,3001,3096,3208,3330,3426,3531,3630,3762,3898,4000,4093,4166,4242,4323,4407,4508,4585,4664,4759,4853,4944,5038,5122,5221,5317,5415,5527,5604,5700,5792"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,332,443,1470,1546,1646,1764,1901,2025,2175,2256,2351,2437,2536,2647,2764,2864,2988,3109,3237,3399,3520,3638,3758,3884,3971,4066,4178,4300,4396,4501,4600,4732,4868,4970,5261,5420,6127,6279,6464,6836,6913,6992,7087,7181,7272,7366,7450,7549,7645,7743,7855,7932,8028", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "213,327,438,550,1541,1641,1759,1896,2020,2170,2251,2346,2432,2531,2642,2759,2859,2983,3104,3232,3394,3515,3633,3753,3879,3966,4061,4173,4295,4391,4496,4595,4727,4863,4965,5058,5329,5491,6203,6358,6560,6908,6987,7082,7176,7267,7361,7445,7544,7640,7738,7850,7927,8023,8115"}}]}]}