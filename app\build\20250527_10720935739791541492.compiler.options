"-Xallow-no-source-files" "-classpath" "G:\\testmobileapp\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08ce6eae71c21e358896db8a83f38074\\transformed\\material3-1.1.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2416dfaca5b0e9fc7e72eeb5afeefa4c\\transformed\\ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\20a3d547f1b6b156be39fd6c7993077d\\transformed\\material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc226574932bf415f461ddb1496148e6\\transformed\\material-ripple-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43d21714fc9528fa1832d91db182b05a\\transformed\\foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\091426101e39aa75f73e7632bb987b8f\\transformed\\foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\77c3e2624e21a3b0d762b4ac37887fe7\\transformed\\animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\22b305df393376e2cc4158e3b6e35988\\transformed\\animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6bab5e4c74378cb5f398847fc5be60be\\transformed\\ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb50ebd5e57b1af081e1ee0e22a55c1f\\transformed\\ui-tooling-data-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2c20df224c6520c6b608f03a2ce86c74\\transformed\\ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\34b70eade5854bc809fb27c191153021\\transformed\\ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f530ee652ec5d62ff41953b223a23fef\\transformed\\ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\912a50a2d00a0d83aec70bf3a4bf49ff\\transformed\\ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b494838808ce1a7b740f6fd298e0ffa3\\transformed\\ui-tooling-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62e9eb8f4931321e25560038969789a2\\transformed\\ui-test-manifest-1.5.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\76e5a122d232866a4f2652e12c6678d9\\transformed\\activity-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79c5f2276d75b40eb4ddd600e81cfd79\\transformed\\activity-compose-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f44e3c9f4295216efd7da828317ec625\\transformed\\activity-ktx-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb550abb389ece5117459a0915789849\\transformed\\core-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a9251e33ac6988040cd836ee810f7651\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895ff959ec0555eaf657d0dc12563bf2\\transformed\\lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b9d07cff4e52aeb11b766fccf0ec0f04\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13a80645873d9cb2af688867e53604f0\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c28ba9ff74420c1c93403676fcd6fd6\\transformed\\lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\485593fd8ff14f66e477ed8678f2602a\\transformed\\lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\135c559c379e5ab42827c4d1a8b8550b\\transformed\\core-ktx-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\819e4c4efec0e57bae967f0ab17df0ef\\transformed\\savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\779f48533c6a155c740ddcbb87a1e22a\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f83526810bc54fd3f818b128a5d9a85e\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\610fcb49f8f9c0058ad840a8b2933a1f\\transformed\\runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\019351956902e67e998c02f7c67d6321\\transformed\\runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.6.0\\a7257339a052df0f91433cf9651231bbb802b502\\annotation-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b5721825e37547b9b8a6e82461439b5\\transformed\\annotation-experimental-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.1\\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\\kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.1\\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\\kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.8.20\\73576ddf378c5b4f1f6b449fe6b119b8183fc078\\kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.8.20\\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\\kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.20\\e58b4816ac517e9cc5df1db051120c63d4cde669\\kotlin-stdlib-1.9.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;G:\\testmobileapp\\android-sdk\\platforms\\android-34\\android.jar;G:\\testmobileapp\\android-sdk\\build-tools\\33.0.1\\core-lambda-stubs.jar" "-d" "G:\\testmobileapp\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.compose.compiler\\compiler\\1.5.4\\8c91757c3fef5d486f6dbb6376ed051b80847746\\compiler-1.5.4.jar" "-P" "plugin:androidx.compose.plugins.idea:enabled=true" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:sourceInformation=true" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:liveLiterals=true" "-Xallow-unstable-dependencies" "G:\\testmobileapp\\app\\src\\main\\java\\com\\example\\myapp\\MainActivity.kt"