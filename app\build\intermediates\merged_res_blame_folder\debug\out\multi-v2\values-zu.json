{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,997,1065,1153,1241,1317,1396,1466", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,992,1060,1148,1236,1312,1391,1461,1585"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1288,1383,5124,5229,5408,5577,5659,5748,5841,5924,5992,6060,6148,6318,6679,6758,6828", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "1378,1460,5224,5329,5493,5654,5743,5836,5919,5987,6055,6143,6231,6389,6753,6823,6947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "558,656,760,859,962,1068,1175,6476", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "651,755,854,957,1063,1170,1283,6572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,395,508,585,679,791,924,1037,1188,1269,1365,1453,1545,1661,1778,1879,2019,2150,2283,2451,2579,2701,2823,2948,3037,3133,3257,3395,3490,3587,3692,3827,3964,4070,4167,4241,4320,4402,4484,4586,4662,4743,4847,4945,5041,5135,5219,5321,5423,5520,5638,5714,5817", "endColumns": "113,114,110,112,76,93,111,132,112,150,80,95,87,91,115,116,100,139,130,132,167,127,121,121,124,88,95,123,137,94,96,104,134,136,105,96,73,78,81,81,101,75,80,103,97,95,93,83,101,101,96,117,75,102,95", "endOffsets": "164,279,390,503,580,674,786,919,1032,1183,1264,1360,1448,1540,1656,1773,1874,2014,2145,2278,2446,2574,2696,2818,2943,3032,3128,3252,3390,3485,3582,3687,3822,3959,4065,4162,4236,4315,4397,4479,4581,4657,4738,4842,4940,5036,5130,5214,5316,5418,5515,5633,5709,5812,5908"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,334,445,1465,1542,1636,1748,1881,1994,2145,2226,2322,2410,2502,2618,2735,2836,2976,3107,3240,3408,3536,3658,3780,3905,3994,4090,4214,4352,4447,4544,4649,4784,4921,5027,5334,5498,6236,6394,6577,6952,7028,7109,7213,7311,7407,7501,7585,7687,7789,7886,8004,8080,8183", "endColumns": "113,114,110,112,76,93,111,132,112,150,80,95,87,91,115,116,100,139,130,132,167,127,121,121,124,88,95,123,137,94,96,104,134,136,105,96,73,78,81,81,101,75,80,103,97,95,93,83,101,101,96,117,75,102,95", "endOffsets": "214,329,440,553,1537,1631,1743,1876,1989,2140,2221,2317,2405,2497,2613,2730,2831,2971,3102,3235,3403,3531,3653,3775,3900,3989,4085,4209,4347,4442,4539,4644,4779,4916,5022,5119,5403,5572,6313,6471,6674,7023,7104,7208,7306,7402,7496,7580,7682,7784,7881,7999,8075,8178,8274"}}]}]}