{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,279,397,508,585,680,793,927,1040,1173,1253,1348,1436,1528,1641,1761,1861,1997,2129,2269,2434,2556,2673,2795,2913,3002,3098,3214,3334,3429,3528,3630,3765,3908,4015,4109,4181,4259,4348,4431,4541,4617,4700,4799,4900,4987,5084,5168,5270,5365,5462,5576,5652,5751", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "161,274,392,503,580,675,788,922,1035,1168,1248,1343,1431,1523,1636,1756,1856,1992,2124,2264,2429,2551,2668,2790,2908,2997,3093,3209,3329,3424,3523,3625,3760,3903,4010,4104,4176,4254,4343,4426,4536,4612,4695,4794,4895,4982,5079,5163,5265,5360,5457,5571,5647,5746,5839"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,329,447,1475,1552,1647,1760,1894,2007,2140,2220,2315,2403,2495,2608,2728,2828,2964,3096,3236,3401,3523,3640,3762,3880,3969,4065,4181,4301,4396,4495,4597,4732,4875,4982,5290,5439,6171,6332,6516,6896,6972,7055,7154,7255,7342,7439,7523,7625,7720,7817,7931,8007,8106", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "211,324,442,553,1547,1642,1755,1889,2002,2135,2215,2310,2398,2490,2603,2723,2823,2959,3091,3231,3396,3518,3635,3757,3875,3964,4060,4176,4296,4391,4490,4592,4727,4870,4977,5071,5357,5512,6255,6410,6621,6967,7050,7149,7250,7337,7434,7518,7620,7715,7812,7926,8002,8101,8194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,990,1060,1143,1230,1302,1384,1452", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,985,1055,1138,1225,1297,1379,1447,1567"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1295,1390,5076,5185,5362,5517,5594,5687,5777,5860,5931,6001,6084,6260,6626,6708,6776", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "1385,1470,5180,5285,5434,5589,5682,5772,5855,5926,5996,6079,6166,6327,6703,6771,6891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "558,655,757,855,954,1068,1173,6415", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "650,752,850,949,1063,1168,1290,6511"}}]}]}