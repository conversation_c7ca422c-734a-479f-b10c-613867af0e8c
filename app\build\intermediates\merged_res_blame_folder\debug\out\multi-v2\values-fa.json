{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "552,651,753,852,952,1053,1159,6296", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "646,748,847,947,1048,1154,1271,6392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,277,387,502,578,670,781,916,1028,1159,1240,1339,1427,1520,1630,1749,1853,1982,2109,2227,2388,2506,2615,2728,2842,2931,3025,3142,3270,3373,3472,3573,3699,3831,3933,4038,4114,4193,4275,4355,4450,4528,4608,4705,4802,4895,4991,5074,5174,5270,5368,5484,5562,5662", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "162,272,382,497,573,665,776,911,1023,1154,1235,1334,1422,1515,1625,1744,1848,1977,2104,2222,2383,2501,2610,2723,2837,2926,3020,3137,3265,3368,3467,3568,3694,3826,3928,4033,4109,4188,4270,4350,4445,4523,4603,4700,4797,4890,4986,5069,5169,5265,5363,5479,5557,5657,5751"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,327,437,1442,1518,1610,1721,1856,1968,2099,2180,2279,2367,2460,2570,2689,2793,2922,3049,3167,3328,3446,3555,3668,3782,3871,3965,4082,4210,4313,4412,4513,4639,4771,4873,5170,5332,6060,6216,6397,6751,6829,6909,7006,7103,7196,7292,7375,7475,7571,7669,7785,7863,7963", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "212,322,432,547,1513,1605,1716,1851,1963,2094,2175,2274,2362,2455,2565,2684,2788,2917,3044,3162,3323,3441,3550,3663,3777,3866,3960,4077,4205,4308,4407,4508,4634,4766,4868,4973,5241,5406,6137,6291,6487,6824,6904,7001,7098,7191,7287,7370,7470,7566,7664,7780,7858,7958,8052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,969,1035,1116,1198,1272,1347,1414", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,964,1030,1111,1193,1267,1342,1409,1526"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1276,1363,4978,5072,5246,5411,5493,5596,5681,5764,5831,5897,5978,6142,6492,6567,6634", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "1358,1437,5067,5165,5327,5488,5591,5676,5759,5826,5892,5973,6055,6211,6562,6629,6746"}}]}]}