{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,601,697,811,950,1064,1208,1289,1387,1480,1578,1685,1799,1902,2036,2165,2305,2478,2598,2714,2833,2960,3054,3146,3263,3394,3493,3603,3714,3846,3983,4090,4190,4273,4351,4434,4516,4623,4699,4779,4876,4978,5074,5169,5254,5361,5458,5557,5672,5748,5861", "endColumns": "116,114,118,116,77,95,113,138,113,143,80,97,92,97,106,113,102,133,128,139,172,119,115,118,126,93,91,116,130,98,109,110,131,136,106,99,82,77,82,81,106,75,79,96,101,95,94,84,106,96,98,114,75,112,104", "endOffsets": "167,282,401,518,596,692,806,945,1059,1203,1284,1382,1475,1573,1680,1794,1897,2031,2160,2300,2473,2593,2709,2828,2955,3049,3141,3258,3389,3488,3598,3709,3841,3978,4085,4185,4268,4346,4429,4511,4618,4694,4774,4871,4973,5069,5164,5249,5356,5453,5552,5667,5743,5856,5961"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,337,456,1483,1561,1657,1771,1910,2024,2168,2249,2347,2440,2538,2645,2759,2862,2996,3125,3265,3438,3558,3674,3793,3920,4014,4106,4223,4354,4453,4563,4674,4806,4943,5050,5351,5523,6272,6432,6615,6995,7071,7151,7248,7350,7446,7541,7626,7733,7830,7929,8044,8120,8233", "endColumns": "116,114,118,116,77,95,113,138,113,143,80,97,92,97,106,113,102,133,128,139,172,119,115,118,126,93,91,116,130,98,109,110,131,136,106,99,82,77,82,81,106,75,79,96,101,95,94,84,106,96,98,114,75,112,104", "endOffsets": "217,332,451,568,1556,1652,1766,1905,2019,2163,2244,2342,2435,2533,2640,2754,2857,2991,3120,3260,3433,3553,3669,3788,3915,4009,4101,4218,4349,4448,4558,4669,4801,4938,5045,5145,5429,5596,6350,6509,6717,7066,7146,7243,7345,7441,7536,7621,7728,7825,7924,8039,8115,8228,8333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,672,774,874,972,1079,1185,6514", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "667,769,869,967,1074,1180,1300,6610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1305,1401,5150,5248,5434,5601,5680,5773,5865,5952,6025,6095,6181,6355,6722,6804,6874", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "1396,1478,5243,5346,5518,5675,5768,5860,5947,6020,6090,6176,6267,6427,6799,6869,6990"}}]}]}