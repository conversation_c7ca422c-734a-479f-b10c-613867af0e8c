[{"merged": "com.example.myapp-merged_res-44:/drawable_ic_launcher.xml.flat", "source": "com.example.myapp-main-46:/drawable/ic_launcher.xml"}, {"merged": "com.example.myapp-merged_res-44:/xml_data_extraction_rules.xml.flat", "source": "com.example.myapp-main-46:/xml/data_extraction_rules.xml"}, {"merged": "com.example.myapp-merged_res-44:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.myapp-main-46:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.myapp-merged_res-44:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.myapp-main-46:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.myapp-merged_res-44:/drawable_ic_launcher_background.xml.flat", "source": "com.example.myapp-main-46:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.myapp-merged_res-44:/xml_backup_rules.xml.flat", "source": "com.example.myapp-main-46:/xml/backup_rules.xml"}, {"merged": "com.example.myapp-merged_res-44:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.myapp-main-46:/mipmap-anydpi-v26/ic_launcher_round.xml"}]