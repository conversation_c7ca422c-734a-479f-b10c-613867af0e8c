{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-42:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb7c64e3f48ba958dd371523c91f674c\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,994,1061,1142,1231,1303,1384,1450", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,989,1056,1137,1226,1298,1379,1445,1562"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1304,1404,5091,5188,5361,5535,5616,5704,5796,5878,5947,6014,6095,6268,6625,6706,6772", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "1399,1487,5183,5284,5447,5611,5699,5791,5873,5942,6009,6090,6179,6335,6701,6767,6884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\628497733af0f2bc2472b0cf306fe3b5\\transformed\\material3-1.1.2\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,297,406,527,612,710,826,958,1075,1217,1298,1397,1484,1578,1686,1804,1908,2043,2176,2302,2462,2583,2694,2809,2922,3010,3105,3218,3346,3448,3549,3651,3784,3923,4029,4126,4198,4281,4365,4448,4549,4625,4705,4801,4898,4991,5085,5169,5269,5365,5462,5583,5659,5757", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "174,292,401,522,607,705,821,953,1070,1212,1293,1392,1479,1573,1681,1799,1903,2038,2171,2297,2457,2578,2689,2804,2917,3005,3100,3213,3341,3443,3544,3646,3779,3918,4024,4121,4193,4276,4360,4443,4544,4620,4700,4796,4893,4986,5080,5164,5264,5360,5457,5578,5654,5752,5846"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,229,347,456,1492,1577,1675,1791,1923,2040,2182,2263,2362,2449,2543,2651,2769,2873,3008,3141,3267,3427,3548,3659,3774,3887,3975,4070,4183,4311,4413,4514,4616,4749,4888,4994,5289,5452,6184,6340,6524,6889,6965,7045,7141,7238,7331,7425,7509,7609,7705,7802,7923,7999,8097", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "224,342,451,572,1572,1670,1786,1918,2035,2177,2258,2357,2444,2538,2646,2764,2868,3003,3136,3262,3422,3543,3654,3769,3882,3970,4065,4178,4306,4408,4509,4611,4744,4883,4989,5086,5356,5530,6263,6418,6620,6960,7040,7136,7233,7326,7420,7504,7604,7700,7797,7918,7994,8092,8186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4274967ed742c2d626134066010c679b\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "577,678,780,883,987,1088,1193,6423", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "673,775,878,982,1083,1188,1299,6519"}}]}]}