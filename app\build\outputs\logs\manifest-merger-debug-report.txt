-- Merging decision tree log ---
manifest
ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml:2:1-28:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\628497733af0f2bc2472b0cf306fe3b5\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b64b95852f06f4f48e4e0f5f368290ea\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\78ad4ec532a8738ba039948d941bd685\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c7a5d20bfcb93ccf2a46e41a00975a10\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\91a84c061b58fa992d8139c2b0d0431e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\60f26b78a7ed840d514559bc8b192a73\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf407094a58fb1b9826f5ab3136103\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a6d76b378789f4a3d75e341a4a79b9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a525bf17a8c36e1c7d9389acae9d62b9\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a1b72a286edb6ac0f032bd45cff52714\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7f0a5bbc6e87341c286372b437c41a54\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\47161ee0607983a59e5bcc50a2dc298b\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\92e538eff1d92df61f06535534b89053\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\75025b1979cad502010c07c705fbd1fd\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9a568017e57f469afaf259f1b375c750\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0dd6bcc24cbdd27f0858d009aeef005f\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cbdd11d338db83f1a3e36cccdc300d88\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c3f46ecd18659ff48aca3e3a20b388ea\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb7c64e3f48ba958dd371523c91f674c\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfed34a9adafd2244cb233d5b400b4cb\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\95514292aa8f77c22571cd8d9ad129a1\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\199befe624d8afc73908bc13a29f1578\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\447f1f6337f6187d622bd6308eb1c435\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\637f8c04801887031da0ccafb3cf3816\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\32a02bbdf1653504207bea38b262299d\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be09877cc53f105ec1fa77a6f1a1cb3e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e18944e01a5610eacdc2ad82266cd21a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8c2e0c3da23ddd9b68114be347ff71a\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c521150f20fb9c928e2646cdcc1681bb\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\660ed800afaae36f777ca7e2e2cbd08b\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5939c1e3ccaff0c52f09c608f7ed8759\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\b2d5da9c464ec3aac7518def83b5f5e5\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0d685a034f2a3305cdc42438f3db0bd8\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e87de3766d8255482c509b339ceeb0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\91ef8372d78a1a250fe056151b67782a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d18d4dad1fba5c91567b8440afcc1073\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e47cc426fe1c56edf309f94cb40ec02f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:5:5-26:19
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml:5:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\91ef8372d78a1a250fe056151b67782a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\91ef8372d78a1a250fe056151b67782a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:13:9-43
	android:dataExtractionRules
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:7:9-65
activity#com.example.myapp.MainActivity
ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:19:13-47
	android:name
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from G:\testmobileapp\app\src\main\AndroidManifest.xml:23:27-74
uses-sdk
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\628497733af0f2bc2472b0cf306fe3b5\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\628497733af0f2bc2472b0cf306fe3b5\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b64b95852f06f4f48e4e0f5f368290ea\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b64b95852f06f4f48e4e0f5f368290ea\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\78ad4ec532a8738ba039948d941bd685\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\78ad4ec532a8738ba039948d941bd685\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c7a5d20bfcb93ccf2a46e41a00975a10\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c7a5d20bfcb93ccf2a46e41a00975a10\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\91a84c061b58fa992d8139c2b0d0431e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\91a84c061b58fa992d8139c2b0d0431e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\60f26b78a7ed840d514559bc8b192a73\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\60f26b78a7ed840d514559bc8b192a73\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf407094a58fb1b9826f5ab3136103\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5dcf407094a58fb1b9826f5ab3136103\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a6d76b378789f4a3d75e341a4a79b9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\89a6d76b378789f4a3d75e341a4a79b9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a525bf17a8c36e1c7d9389acae9d62b9\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a525bf17a8c36e1c7d9389acae9d62b9\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a1b72a286edb6ac0f032bd45cff52714\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a1b72a286edb6ac0f032bd45cff52714\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7f0a5bbc6e87341c286372b437c41a54\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7f0a5bbc6e87341c286372b437c41a54\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\47161ee0607983a59e5bcc50a2dc298b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\47161ee0607983a59e5bcc50a2dc298b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\92e538eff1d92df61f06535534b89053\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\92e538eff1d92df61f06535534b89053\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\75025b1979cad502010c07c705fbd1fd\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\75025b1979cad502010c07c705fbd1fd\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9a568017e57f469afaf259f1b375c750\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9a568017e57f469afaf259f1b375c750\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0dd6bcc24cbdd27f0858d009aeef005f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0dd6bcc24cbdd27f0858d009aeef005f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cbdd11d338db83f1a3e36cccdc300d88\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cbdd11d338db83f1a3e36cccdc300d88\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c3f46ecd18659ff48aca3e3a20b388ea\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c3f46ecd18659ff48aca3e3a20b388ea\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb7c64e3f48ba958dd371523c91f674c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb7c64e3f48ba958dd371523c91f674c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfed34a9adafd2244cb233d5b400b4cb\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfed34a9adafd2244cb233d5b400b4cb\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\95514292aa8f77c22571cd8d9ad129a1\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\95514292aa8f77c22571cd8d9ad129a1\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\199befe624d8afc73908bc13a29f1578\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\199befe624d8afc73908bc13a29f1578\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\447f1f6337f6187d622bd6308eb1c435\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\447f1f6337f6187d622bd6308eb1c435\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\637f8c04801887031da0ccafb3cf3816\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\637f8c04801887031da0ccafb3cf3816\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\32a02bbdf1653504207bea38b262299d\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\32a02bbdf1653504207bea38b262299d\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be09877cc53f105ec1fa77a6f1a1cb3e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be09877cc53f105ec1fa77a6f1a1cb3e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e18944e01a5610eacdc2ad82266cd21a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e18944e01a5610eacdc2ad82266cd21a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8c2e0c3da23ddd9b68114be347ff71a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c8c2e0c3da23ddd9b68114be347ff71a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c521150f20fb9c928e2646cdcc1681bb\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c521150f20fb9c928e2646cdcc1681bb\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\660ed800afaae36f777ca7e2e2cbd08b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\660ed800afaae36f777ca7e2e2cbd08b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5939c1e3ccaff0c52f09c608f7ed8759\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5939c1e3ccaff0c52f09c608f7ed8759\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\b2d5da9c464ec3aac7518def83b5f5e5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\b2d5da9c464ec3aac7518def83b5f5e5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0d685a034f2a3305cdc42438f3db0bd8\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0d685a034f2a3305cdc42438f3db0bd8\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e87de3766d8255482c509b339ceeb0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e87de3766d8255482c509b339ceeb0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\91ef8372d78a1a250fe056151b67782a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\91ef8372d78a1a250fe056151b67782a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d18d4dad1fba5c91567b8440afcc1073\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d18d4dad1fba5c91567b8440afcc1073\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e47cc426fe1c56edf309f94cb40ec02f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e47cc426fe1c56edf309f94cb40ec02f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\testmobileapp\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
