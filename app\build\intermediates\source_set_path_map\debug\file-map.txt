com.example.myapp-runtime-release-0 C:\Users\<USER>\.gradle\caches\transforms-3\0d685a034f2a3305cdc42438f3db0bd8\transformed\runtime-release\res
com.example.myapp-ui-tooling-preview-release-1 C:\Users\<USER>\.gradle\caches\transforms-3\0dd6bcc24cbdd27f0858d009aeef005f\transformed\ui-tooling-preview-release\res
com.example.myapp-profileinstaller-1.3.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\17a042a5895249c33aafd1150f35b19b\transformed\profileinstaller-1.3.0\res
com.example.myapp-lifecycle-livedata-core-2.7.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\199befe624d8afc73908bc13a29f1578\transformed\lifecycle-livedata-core-2.7.0\res
com.example.myapp-lifecycle-runtime-ktx-2.7.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\32a02bbdf1653504207bea38b262299d\transformed\lifecycle-runtime-ktx-2.7.0\res
com.example.myapp-core-1.12.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\4274967ed742c2d626134066010c679b\transformed\core-1.12.0\res
com.example.myapp-lifecycle-viewmodel-ktx-2.7.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\447f1f6337f6187d622bd6308eb1c435\transformed\lifecycle-viewmodel-ktx-2.7.0\res
com.example.myapp-emoji2-1.4.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\4665cb7cdd1909e2a3a003826a19e26b\transformed\emoji2-1.4.0\res
com.example.myapp-ui-unit-release-8 C:\Users\<USER>\.gradle\caches\transforms-3\47161ee0607983a59e5bcc50a2dc298b\transformed\ui-unit-release\res
com.example.myapp-annotation-experimental-1.3.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\5939c1e3ccaff0c52f09c608f7ed8759\transformed\annotation-experimental-1.3.0\res
com.example.myapp-animation-core-release-10 C:\Users\<USER>\.gradle\caches\transforms-3\5dcf407094a58fb1b9826f5ab3136103\transformed\animation-core-release\res
com.example.myapp-material-release-11 C:\Users\<USER>\.gradle\caches\transforms-3\60f26b78a7ed840d514559bc8b192a73\transformed\material-release\res
com.example.myapp-material3-1.1.2-12 C:\Users\<USER>\.gradle\caches\transforms-3\628497733af0f2bc2472b0cf306fe3b5\transformed\material3-1.1.2\res
com.example.myapp-lifecycle-viewmodel-2.7.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\637f8c04801887031da0ccafb3cf3816\transformed\lifecycle-viewmodel-2.7.0\res
com.example.myapp-core-ktx-1.12.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\660ed800afaae36f777ca7e2e2cbd08b\transformed\core-ktx-1.12.0\res
com.example.myapp-ui-util-release-15 C:\Users\<USER>\.gradle\caches\transforms-3\75025b1979cad502010c07c705fbd1fd\transformed\ui-util-release\res
com.example.myapp-ui-test-manifest-1.5.4-16 C:\Users\<USER>\.gradle\caches\transforms-3\7878c737966b95aad5d6f9bd00baa090\transformed\ui-test-manifest-1.5.4\res
com.example.myapp-activity-ktx-1.8.2-17 C:\Users\<USER>\.gradle\caches\transforms-3\78ad4ec532a8738ba039948d941bd685\transformed\activity-ktx-1.8.2\res
com.example.myapp-lifecycle-process-2.7.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\7b45e4af23fa43939838097d77cf7923\transformed\lifecycle-process-2.7.0\res
com.example.myapp-ui-tooling-data-release-19 C:\Users\<USER>\.gradle\caches\transforms-3\7f0a5bbc6e87341c286372b437c41a54\transformed\ui-tooling-data-release\res
com.example.myapp-animation-release-20 C:\Users\<USER>\.gradle\caches\transforms-3\89a6d76b378789f4a3d75e341a4a79b9\transformed\animation-release\res
com.example.myapp-material-icons-core-release-21 C:\Users\<USER>\.gradle\caches\transforms-3\91a84c061b58fa992d8139c2b0d0431e\transformed\material-icons-core-release\res
com.example.myapp-ui-geometry-release-22 C:\Users\<USER>\.gradle\caches\transforms-3\92e538eff1d92df61f06535534b89053\transformed\ui-geometry-release\res
com.example.myapp-customview-poolingcontainer-1.0.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\95514292aa8f77c22571cd8d9ad129a1\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapp-ui-text-release-24 C:\Users\<USER>\.gradle\caches\transforms-3\9a568017e57f469afaf259f1b375c750\transformed\ui-text-release\res
com.example.myapp-foundation-release-25 C:\Users\<USER>\.gradle\caches\transforms-3\a1b72a286edb6ac0f032bd45cff52714\transformed\foundation-release\res
com.example.myapp-startup-runtime-1.1.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\a3617a96bb7c392189dda1fe0f3a5ab8\transformed\startup-runtime-1.1.1\res
com.example.myapp-foundation-layout-release-27 C:\Users\<USER>\.gradle\caches\transforms-3\a525bf17a8c36e1c7d9389acae9d62b9\transformed\foundation-layout-release\res
com.example.myapp-runtime-saveable-release-28 C:\Users\<USER>\.gradle\caches\transforms-3\b2d5da9c464ec3aac7518def83b5f5e5\transformed\runtime-saveable-release\res
com.example.myapp-activity-1.8.2-29 C:\Users\<USER>\.gradle\caches\transforms-3\b64b95852f06f4f48e4e0f5f368290ea\transformed\activity-1.8.2\res
com.example.myapp-lifecycle-runtime-2.7.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\be09877cc53f105ec1fa77a6f1a1cb3e\transformed\lifecycle-runtime-2.7.0\res
com.example.myapp-activity-compose-1.8.2-31 C:\Users\<USER>\.gradle\caches\transforms-3\c3f46ecd18659ff48aca3e3a20b388ea\transformed\activity-compose-1.8.2\res
com.example.myapp-lifecycle-viewmodel-savedstate-2.7.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\c521150f20fb9c928e2646cdcc1681bb\transformed\lifecycle-viewmodel-savedstate-2.7.0\res
com.example.myapp-material-ripple-release-33 C:\Users\<USER>\.gradle\caches\transforms-3\c7a5d20bfcb93ccf2a46e41a00975a10\transformed\material-ripple-release\res
com.example.myapp-savedstate-1.2.1-34 C:\Users\<USER>\.gradle\caches\transforms-3\c8c2e0c3da23ddd9b68114be347ff71a\transformed\savedstate-1.2.1\res
com.example.myapp-ui-graphics-release-35 C:\Users\<USER>\.gradle\caches\transforms-3\cbdd11d338db83f1a3e36cccdc300d88\transformed\ui-graphics-release\res
com.example.myapp-core-runtime-2.2.0-36 C:\Users\<USER>\.gradle\caches\transforms-3\d18d4dad1fba5c91567b8440afcc1073\transformed\core-runtime-2.2.0\res
com.example.myapp-ui-tooling-release-37 C:\Users\<USER>\.gradle\caches\transforms-3\d96381f5ec39bbc4f527d3ed3790ca2d\transformed\ui-tooling-release\res
com.example.myapp-savedstate-ktx-1.2.1-38 C:\Users\<USER>\.gradle\caches\transforms-3\e18944e01a5610eacdc2ad82266cd21a\transformed\savedstate-ktx-1.2.1\res
com.example.myapp-ui-release-39 C:\Users\<USER>\.gradle\caches\transforms-3\fb7c64e3f48ba958dd371523c91f674c\transformed\ui-release\res
com.example.myapp-resValues-40 G:\testmobileapp\app\build\generated\res\resValues\debug
com.example.myapp-packageDebugResources-41 G:\testmobileapp\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapp-packageDebugResources-42 G:\testmobileapp\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapp-merged_res-43 G:\testmobileapp\app\build\intermediates\merged_res\debug
com.example.myapp-debug-44 G:\testmobileapp\app\src\debug\res
com.example.myapp-main-45 G:\testmobileapp\app\src\main\res
