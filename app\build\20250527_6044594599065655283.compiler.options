"-Xallow-no-source-files" "-classpath" "G:\\testmobileapp\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\17aac98f604482150c2a1af85004d124\\transformed\\activity-ktx-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e05f7c6b76f0cf8432cd35aa4424dc1a\\transformed\\activity-compose-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e0787e67c8a1a0b0557fc34c12536bae\\transformed\\material3-1.1.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19f7320ce050657b3d0801af216ff759\\transformed\\ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f322167e9a108d9bdc10bdadecdf150\\transformed\\material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4009c79abc16bb432d07f96f74549621\\transformed\\material-ripple-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f4beadbede14d1061d56cf11faff4b1\\transformed\\foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\da37b79a773f1259ff1d02f837fd96da\\transformed\\foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6e649554c8dbeacd48baae129c255bc0\\transformed\\animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9bc9d77f4307875a467b9adb7176b60d\\transformed\\animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\092899f51eb2c00e014b0a5edf3807b6\\transformed\\ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2daea69213784d4a71b21fb62c1b1540\\transformed\\ui-tooling-data-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a024343fd82108e910754680d6444e2e\\transformed\\ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af231cfbf04fd3b7d72de6a0533fd8ed\\transformed\\ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\30aee7773763720ecab4e7b4b82b24d5\\transformed\\ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\936a898f025566c71fb8a55fd172a8ac\\transformed\\ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\672924b4a0083b49840c139ad6bb85eb\\transformed\\ui-tooling-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\75a4baddfc488e2382d736872fcadccb\\transformed\\ui-test-manifest-1.5.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b32bfbcd4f974508e85f36e4f7a55a84\\transformed\\activity-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3481d36cd95d01c4c6500958e11a03e8\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c9a4f863ae39716d137c933aff3881d3\\transformed\\lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b01262be55eaa1bf6ae3c6842b6b426\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2d4d19bee09884b25b3a56bffd28d75\\transformed\\lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e488e5438481be886442e65790ba303f\\transformed\\core-ktx-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c7b9ead2d8a1a3491d3cdbff89283892\\transformed\\core-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1a010ef813f5997a0c305cb1366406fc\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\04d42e3b99ab6cd6b0b01428a37de1aa\\transformed\\lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\39a36a5a482f45d2fbff9a10c6bb3415\\transformed\\runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5d847b4fbef0a47e8c5729f03e587eb\\transformed\\runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.1\\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\\kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.1\\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\\kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.9.10\\c7510d64a83411a649c76f2778304ddf71d7437b\\kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.9.10\\bc5bfc2690338defd5195b05c57562f2194eeb10\\kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6e2c024cc136d33073894aafaa73d48b\\transformed\\savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\82ee5f66756668e4966a34d994fa35ab\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fee9a294d9f5d8a65c2383991ef25d14\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.6.0\\a7257339a052df0f91433cf9651231bbb802b502\\annotation-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c963e22ac2e36294182dc513627dc216\\transformed\\annotation-experimental-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.10\\72812e8a368917ab5c0a5081b56915ffdfec93b7\\kotlin-stdlib-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-common\\1.9.10\\dafaf2c27f27c09220cee312df10917d9a5d97ce\\kotlin-stdlib-common-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;G:\\testmobileapp\\android-sdk\\platforms\\android-34\\android.jar;G:\\testmobileapp\\android-sdk\\build-tools\\33.0.1\\core-lambda-stubs.jar" "-d" "G:\\testmobileapp\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.compose.compiler\\compiler\\1.5.4\\8c91757c3fef5d486f6dbb6376ed051b80847746\\compiler-1.5.4.jar" "-P" "plugin:androidx.compose.plugins.idea:enabled=true" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:sourceInformation=true" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:liveLiterals=true" "-Xallow-unstable-dependencies" "G:\\testmobileapp\\app\\src\\main\\java\\com\\example\\myapp\\MainActivity.kt"